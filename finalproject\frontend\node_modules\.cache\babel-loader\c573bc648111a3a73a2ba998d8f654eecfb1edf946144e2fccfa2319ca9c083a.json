{"ast": null, "code": "// WebSocket服务 - 处理实时通信\n\nclass WebSocketService {\n  constructor() {\n    this.socket = null;\n    this.url = '';\n    this.token = null;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectInterval = 3000;\n    this.isConnecting = false;\n    this.handlers = {};\n    this.heartbeatInterval = null;\n    // 处理连接打开\n    this.handleOpen = event => {\n      console.log('WebSocket connected successfully');\n      this.isConnecting = false;\n      this.reconnectAttempts = 0;\n\n      // 启动心跳检测\n      this.startHeartbeat();\n\n      // 发送订阅消息\n      this.sendMessage({\n        type: 'subscribe',\n        data: 'user_events'\n      });\n\n      // 调用连接回调\n      if (this.handlers.onConnect) {\n        this.handlers.onConnect();\n      }\n    };\n    // 处理接收消息\n    this.handleMessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        console.log('WebSocket message received:', message);\n\n        // 根据消息类型分发处理\n        switch (message.type) {\n          case 'balance_updated':\n            if (this.handlers.onBalanceUpdate) {\n              this.handlers.onBalanceUpdate(message.data);\n            }\n            break;\n          case 'game_result':\n            if (this.handlers.onGameResult) {\n              this.handlers.onGameResult(message.data);\n            }\n            break;\n          case 'notification':\n            if (this.handlers.onNotification) {\n              this.handlers.onNotification(message.data);\n            }\n            break;\n          case 'transaction_updated':\n            if (this.handlers.onTransactionUpdate) {\n              this.handlers.onTransactionUpdate(message.data);\n            }\n            break;\n          case 'config_updated':\n            if (this.handlers.onConfigUpdate) {\n              this.handlers.onConfigUpdate(message.data);\n            }\n            break;\n          case 'pong':\n            // 心跳响应，不需要处理\n            break;\n          default:\n            console.log('Unknown WebSocket message type:', message.type);\n        }\n      } catch (error) {\n        console.error('Failed to parse WebSocket message:', error);\n      }\n    };\n    // 处理连接关闭\n    this.handleClose = event => {\n      console.log('WebSocket connection closed:', event.code, event.reason);\n      this.isConnecting = false;\n      if (this.heartbeatInterval) {\n        clearInterval(this.heartbeatInterval);\n        this.heartbeatInterval = null;\n      }\n\n      // 调用断开连接回调\n      if (this.handlers.onDisconnect) {\n        this.handlers.onDisconnect();\n      }\n\n      // 如果不是主动关闭，尝试重连\n      if (event.code !== 1000) {\n        this.scheduleReconnect();\n      }\n    };\n    // 处理连接错误\n    this.handleError = event => {\n      console.error('WebSocket error:', event);\n      this.isConnecting = false;\n\n      // 调用错误回调\n      if (this.handlers.onError) {\n        this.handlers.onError(event);\n      }\n    };\n    this.initialize();\n  }\n\n  // 初始化WebSocket服务\n  async initialize() {\n    try {\n      // 使用专门的WebSocket URL或从API URL构建\n      const wsUrl = process.env.REACT_APP_WS_URL;\n      if (wsUrl) {\n        this.url = wsUrl;\n      } else {\n        // 回退到从API URL构建\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8081/api/v1';\n        this.url = baseUrl.replace('http', 'ws').replace('/api/v1', '');\n      }\n      console.log('WebSocket URL:', this.url);\n\n      // 获取认证令牌\n      this.token = localStorage.getItem('token');\n\n      // 如果有令牌，则连接WebSocket\n      if (this.token) {\n        this.connect();\n      } else {\n        console.log('No authentication token found, WebSocket connection deferred');\n      }\n    } catch (error) {\n      console.error('Failed to initialize WebSocketService:', error);\n    }\n  }\n\n  // 设置事件处理器\n  setHandlers(handlers) {\n    this.handlers = {\n      ...this.handlers,\n      ...handlers\n    };\n  }\n\n  // 更新认证令牌\n  updateToken(token) {\n    this.token = token;\n    if (token) {\n      // 有新令牌，连接WebSocket\n      this.connect();\n    } else {\n      // 令牌被清除，断开连接\n      this.disconnect();\n    }\n  }\n\n  // 连接WebSocket\n  connect() {\n    if (this.isConnecting || this.socket && this.socket.readyState === WebSocket.OPEN) {\n      return;\n    }\n    this.isConnecting = true;\n    try {\n      // 确保有认证令牌\n      if (!this.token) {\n        this.token = localStorage.getItem('token');\n        if (!this.token) {\n          console.warn('No authentication token found, cannot connect to WebSocket');\n          this.isConnecting = false;\n          return;\n        }\n      }\n\n      // 构建WebSocket URL，添加认证令牌\n      // 确保URL不重复/ws路径\n      const baseWsUrl = this.url.endsWith('/ws') ? this.url : `${this.url}/ws`;\n      const wsUrl = `${baseWsUrl}?token=${this.token}`;\n      console.log(`Connecting to WebSocket: ${wsUrl}`);\n\n      // 创建WebSocket连接\n      this.socket = new WebSocket(wsUrl);\n\n      // 设置事件处理器\n      this.socket.onopen = this.handleOpen.bind(this);\n      this.socket.onmessage = this.handleMessage.bind(this);\n      this.socket.onclose = this.handleClose.bind(this);\n      this.socket.onerror = this.handleError.bind(this);\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      this.isConnecting = false;\n      this.scheduleReconnect();\n    }\n  }\n\n  // 断开WebSocket连接\n  disconnect() {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n  }\n  // 发送消息\n  sendMessage(message) {\n    if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n      const fullMessage = {\n        ...message,\n        timestamp: Date.now()\n      };\n      this.socket.send(JSON.stringify(fullMessage));\n    } else {\n      console.warn('WebSocket is not connected, cannot send message');\n    }\n  }\n\n  // 启动心跳检测\n  startHeartbeat() {\n    this.heartbeatInterval = setInterval(() => {\n      this.sendMessage({\n        type: 'ping',\n        data: 'ping'\n      });\n    }, 30000); // 每30秒发送一次心跳\n  }\n\n  // 安排重连\n  scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('Max reconnection attempts reached, giving up');\n      return;\n    }\n    this.reconnectAttempts++;\n    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);\n    console.log(`Scheduling WebSocket reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);\n    setTimeout(() => {\n      if (!this.socket || this.socket.readyState === WebSocket.CLOSED) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  // 获取连接状态\n  isConnected() {\n    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;\n  }\n\n  // 获取连接状态文本\n  getConnectionState() {\n    if (!this.socket) return 'Disconnected';\n    switch (this.socket.readyState) {\n      case WebSocket.CONNECTING:\n        return 'Connecting';\n      case WebSocket.OPEN:\n        return 'Connected';\n      case WebSocket.CLOSING:\n        return 'Closing';\n      case WebSocket.CLOSED:\n        return 'Closed';\n      default:\n        return 'Unknown';\n    }\n  }\n}\n\n// 导出单例实例\nexport const webSocketService = new WebSocketService();\nexport default webSocketService;", "map": {"version": 3, "names": ["WebSocketService", "constructor", "socket", "url", "token", "reconnectAttempts", "maxReconnectAttempts", "reconnectInterval", "isConnecting", "handlers", "heartbeatInterval", "handleOpen", "event", "console", "log", "startHeartbeat", "sendMessage", "type", "data", "onConnect", "handleMessage", "message", "JSON", "parse", "onBalanceUpdate", "onGameResult", "onNotification", "onTransactionUpdate", "onConfigUpdate", "error", "handleClose", "code", "reason", "clearInterval", "onDisconnect", "scheduleReconnect", "handleError", "onError", "initialize", "wsUrl", "process", "env", "REACT_APP_WS_URL", "baseUrl", "REACT_APP_API_URL", "replace", "localStorage", "getItem", "connect", "setHandlers", "updateToken", "disconnect", "readyState", "WebSocket", "OPEN", "warn", "baseWsUrl", "endsWith", "onopen", "bind", "onmessage", "onclose", "onerror", "close", "fullMessage", "timestamp", "Date", "now", "send", "stringify", "setInterval", "delay", "Math", "pow", "setTimeout", "CLOSED", "isConnected", "getConnectionState", "CONNECTING", "CLOSING", "webSocketService"], "sources": ["D:/新项目1/finalproject/frontend/src/services/websocket.ts"], "sourcesContent": ["// WebSocket服务 - 处理实时通信\nexport interface WebSocketMessage {\n  type: string;\n  data: any;\n  timestamp: number;\n  user_id?: string;\n}\n\nexport interface WebSocketEventHandlers {\n  onBalanceUpdate?: (data: { user_id: string; balance: number; points_balance: number }) => void;\n  onGameResult?: (data: { game_type: string; result: any }) => void;\n  onNotification?: (data: any) => void;\n  onTransactionUpdate?: (data: any) => void;\n  onConfigUpdate?: (data: { config_type: string; data: any }) => void;\n  onConnect?: () => void;\n  onDisconnect?: () => void;\n  onError?: (error: Event) => void;\n}\n\nclass WebSocketService {\n  private socket: WebSocket | null = null;\n  private url: string = '';\n  private token: string | null = null;\n  private reconnectAttempts: number = 0;\n  private maxReconnectAttempts: number = 5;\n  private reconnectInterval: number = 3000;\n  private isConnecting: boolean = false;\n  private handlers: WebSocketEventHandlers = {};\n  private heartbeatInterval: NodeJS.Timeout | null = null;\n\n  constructor() {\n    this.initialize();\n  }\n\n  // 初始化WebSocket服务\n  private async initialize() {\n    try {\n      // 使用专门的WebSocket URL或从API URL构建\n      const wsUrl = process.env.REACT_APP_WS_URL;\n      if (wsUrl) {\n        this.url = wsUrl;\n      } else {\n        // 回退到从API URL构建\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8081/api/v1';\n        this.url = baseUrl.replace('http', 'ws').replace('/api/v1', '');\n      }\n\n      console.log('WebSocket URL:', this.url);\n      \n      // 获取认证令牌\n      this.token = localStorage.getItem('token');\n      \n      // 如果有令牌，则连接WebSocket\n      if (this.token) {\n        this.connect();\n      } else {\n        console.log('No authentication token found, WebSocket connection deferred');\n      }\n    } catch (error) {\n      console.error('Failed to initialize WebSocketService:', error);\n    }\n  }\n\n  // 设置事件处理器\n  setHandlers(handlers: WebSocketEventHandlers) {\n    this.handlers = { ...this.handlers, ...handlers };\n  }\n\n  // 更新认证令牌\n  updateToken(token: string | null) {\n    this.token = token;\n    \n    if (token) {\n      // 有新令牌，连接WebSocket\n      this.connect();\n    } else {\n      // 令牌被清除，断开连接\n      this.disconnect();\n    }\n  }\n\n  // 连接WebSocket\n  connect() {\n    if (this.isConnecting || (this.socket && this.socket.readyState === WebSocket.OPEN)) {\n      return;\n    }\n\n    this.isConnecting = true;\n\n    try {\n      // 确保有认证令牌\n      if (!this.token) {\n        this.token = localStorage.getItem('token');\n        if (!this.token) {\n          console.warn('No authentication token found, cannot connect to WebSocket');\n          this.isConnecting = false;\n          return;\n        }\n      }\n\n      // 构建WebSocket URL，添加认证令牌\n      // 确保URL不重复/ws路径\n      const baseWsUrl = this.url.endsWith('/ws') ? this.url : `${this.url}/ws`;\n      const wsUrl = `${baseWsUrl}?token=${this.token}`;\n      console.log(`Connecting to WebSocket: ${wsUrl}`);\n\n      // 创建WebSocket连接\n      this.socket = new WebSocket(wsUrl);\n\n      // 设置事件处理器\n      this.socket.onopen = this.handleOpen.bind(this);\n      this.socket.onmessage = this.handleMessage.bind(this);\n      this.socket.onclose = this.handleClose.bind(this);\n      this.socket.onerror = this.handleError.bind(this);\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      this.isConnecting = false;\n      this.scheduleReconnect();\n    }\n  }\n\n  // 断开WebSocket连接\n  disconnect() {\n    if (this.socket) {\n      this.socket.close();\n      this.socket = null;\n    }\n    \n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n    \n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n  }\n\n  // 处理连接打开\n  private handleOpen = (event: Event) => {\n    console.log('WebSocket connected successfully');\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n    \n    // 启动心跳检测\n    this.startHeartbeat();\n    \n    // 发送订阅消息\n    this.sendMessage({\n      type: 'subscribe',\n      data: 'user_events',\n    });\n\n    // 调用连接回调\n    if (this.handlers.onConnect) {\n      this.handlers.onConnect();\n    }\n  };\n\n  // 处理接收消息\n  private handleMessage = (event: MessageEvent) => {\n    try {\n      const message: WebSocketMessage = JSON.parse(event.data);\n      console.log('WebSocket message received:', message);\n\n      // 根据消息类型分发处理\n      switch (message.type) {\n        case 'balance_updated':\n          if (this.handlers.onBalanceUpdate) {\n            this.handlers.onBalanceUpdate(message.data);\n          }\n          break;\n\n        case 'game_result':\n          if (this.handlers.onGameResult) {\n            this.handlers.onGameResult(message.data);\n          }\n          break;\n\n        case 'notification':\n          if (this.handlers.onNotification) {\n            this.handlers.onNotification(message.data);\n          }\n          break;\n\n        case 'transaction_updated':\n          if (this.handlers.onTransactionUpdate) {\n            this.handlers.onTransactionUpdate(message.data);\n          }\n          break;\n\n        case 'config_updated':\n          if (this.handlers.onConfigUpdate) {\n            this.handlers.onConfigUpdate(message.data);\n          }\n          break;\n\n        case 'pong':\n          // 心跳响应，不需要处理\n          break;\n\n        default:\n          console.log('Unknown WebSocket message type:', message.type);\n      }\n    } catch (error) {\n      console.error('Failed to parse WebSocket message:', error);\n    }\n  };\n\n  // 处理连接关闭\n  private handleClose = (event: CloseEvent) => {\n    console.log('WebSocket connection closed:', event.code, event.reason);\n    this.isConnecting = false;\n    \n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n\n    // 调用断开连接回调\n    if (this.handlers.onDisconnect) {\n      this.handlers.onDisconnect();\n    }\n\n    // 如果不是主动关闭，尝试重连\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  };\n\n  // 处理连接错误\n  private handleError = (event: Event) => {\n    console.error('WebSocket error:', event);\n    this.isConnecting = false;\n\n    // 调用错误回调\n    if (this.handlers.onError) {\n      this.handlers.onError(event);\n    }\n  };\n\n  // 发送消息\n  sendMessage(message: Omit<WebSocketMessage, 'timestamp'>) {\n    if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n      const fullMessage: WebSocketMessage = {\n        ...message,\n        timestamp: Date.now(),\n      };\n      this.socket.send(JSON.stringify(fullMessage));\n    } else {\n      console.warn('WebSocket is not connected, cannot send message');\n    }\n  }\n\n  // 启动心跳检测\n  private startHeartbeat() {\n    this.heartbeatInterval = setInterval(() => {\n      this.sendMessage({\n        type: 'ping',\n        data: 'ping',\n      });\n    }, 30000); // 每30秒发送一次心跳\n  }\n\n  // 安排重连\n  private scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('Max reconnection attempts reached, giving up');\n      return;\n    }\n\n    this.reconnectAttempts++;\n    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);\n    \n    console.log(`Scheduling WebSocket reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);\n    \n    setTimeout(() => {\n      if (!this.socket || this.socket.readyState === WebSocket.CLOSED) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  // 获取连接状态\n  isConnected(): boolean {\n    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;\n  }\n\n  // 获取连接状态文本\n  getConnectionState(): string {\n    if (!this.socket) return 'Disconnected';\n    \n    switch (this.socket.readyState) {\n      case WebSocket.CONNECTING: return 'Connecting';\n      case WebSocket.OPEN: return 'Connected';\n      case WebSocket.CLOSING: return 'Closing';\n      case WebSocket.CLOSED: return 'Closed';\n      default: return 'Unknown';\n    }\n  }\n}\n\n// 导出单例实例\nexport const webSocketService = new WebSocketService();\nexport default webSocketService;\n"], "mappings": "AAAA;;AAmBA,MAAMA,gBAAgB,CAAC;EAWrBC,WAAWA,CAAA,EAAG;IAAA,KAVNC,MAAM,GAAqB,IAAI;IAAA,KAC/BC,GAAG,GAAW,EAAE;IAAA,KAChBC,KAAK,GAAkB,IAAI;IAAA,KAC3BC,iBAAiB,GAAW,CAAC;IAAA,KAC7BC,oBAAoB,GAAW,CAAC;IAAA,KAChCC,iBAAiB,GAAW,IAAI;IAAA,KAChCC,YAAY,GAAY,KAAK;IAAA,KAC7BC,QAAQ,GAA2B,CAAC,CAAC;IAAA,KACrCC,iBAAiB,GAA0B,IAAI;IA6GvD;IAAA,KACQC,UAAU,GAAIC,KAAY,IAAK;MACrCC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C,IAAI,CAACN,YAAY,GAAG,KAAK;MACzB,IAAI,CAACH,iBAAiB,GAAG,CAAC;;MAE1B;MACA,IAAI,CAACU,cAAc,CAAC,CAAC;;MAErB;MACA,IAAI,CAACC,WAAW,CAAC;QACfC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACA,IAAI,IAAI,CAACT,QAAQ,CAACU,SAAS,EAAE;QAC3B,IAAI,CAACV,QAAQ,CAACU,SAAS,CAAC,CAAC;MAC3B;IACF,CAAC;IAED;IAAA,KACQC,aAAa,GAAIR,KAAmB,IAAK;MAC/C,IAAI;QACF,MAAMS,OAAyB,GAAGC,IAAI,CAACC,KAAK,CAACX,KAAK,CAACM,IAAI,CAAC;QACxDL,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEO,OAAO,CAAC;;QAEnD;QACA,QAAQA,OAAO,CAACJ,IAAI;UAClB,KAAK,iBAAiB;YACpB,IAAI,IAAI,CAACR,QAAQ,CAACe,eAAe,EAAE;cACjC,IAAI,CAACf,QAAQ,CAACe,eAAe,CAACH,OAAO,CAACH,IAAI,CAAC;YAC7C;YACA;UAEF,KAAK,aAAa;YAChB,IAAI,IAAI,CAACT,QAAQ,CAACgB,YAAY,EAAE;cAC9B,IAAI,CAAChB,QAAQ,CAACgB,YAAY,CAACJ,OAAO,CAACH,IAAI,CAAC;YAC1C;YACA;UAEF,KAAK,cAAc;YACjB,IAAI,IAAI,CAACT,QAAQ,CAACiB,cAAc,EAAE;cAChC,IAAI,CAACjB,QAAQ,CAACiB,cAAc,CAACL,OAAO,CAACH,IAAI,CAAC;YAC5C;YACA;UAEF,KAAK,qBAAqB;YACxB,IAAI,IAAI,CAACT,QAAQ,CAACkB,mBAAmB,EAAE;cACrC,IAAI,CAAClB,QAAQ,CAACkB,mBAAmB,CAACN,OAAO,CAACH,IAAI,CAAC;YACjD;YACA;UAEF,KAAK,gBAAgB;YACnB,IAAI,IAAI,CAACT,QAAQ,CAACmB,cAAc,EAAE;cAChC,IAAI,CAACnB,QAAQ,CAACmB,cAAc,CAACP,OAAO,CAACH,IAAI,CAAC;YAC5C;YACA;UAEF,KAAK,MAAM;YACT;YACA;UAEF;YACEL,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEO,OAAO,CAACJ,IAAI,CAAC;QAChE;MACF,CAAC,CAAC,OAAOY,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;IACF,CAAC;IAED;IAAA,KACQC,WAAW,GAAIlB,KAAiB,IAAK;MAC3CC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,KAAK,CAACmB,IAAI,EAAEnB,KAAK,CAACoB,MAAM,CAAC;MACrE,IAAI,CAACxB,YAAY,GAAG,KAAK;MAEzB,IAAI,IAAI,CAACE,iBAAiB,EAAE;QAC1BuB,aAAa,CAAC,IAAI,CAACvB,iBAAiB,CAAC;QACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC/B;;MAEA;MACA,IAAI,IAAI,CAACD,QAAQ,CAACyB,YAAY,EAAE;QAC9B,IAAI,CAACzB,QAAQ,CAACyB,YAAY,CAAC,CAAC;MAC9B;;MAEA;MACA,IAAItB,KAAK,CAACmB,IAAI,KAAK,IAAI,EAAE;QACvB,IAAI,CAACI,iBAAiB,CAAC,CAAC;MAC1B;IACF,CAAC;IAED;IAAA,KACQC,WAAW,GAAIxB,KAAY,IAAK;MACtCC,OAAO,CAACgB,KAAK,CAAC,kBAAkB,EAAEjB,KAAK,CAAC;MACxC,IAAI,CAACJ,YAAY,GAAG,KAAK;;MAEzB;MACA,IAAI,IAAI,CAACC,QAAQ,CAAC4B,OAAO,EAAE;QACzB,IAAI,CAAC5B,QAAQ,CAAC4B,OAAO,CAACzB,KAAK,CAAC;MAC9B;IACF,CAAC;IA/MC,IAAI,CAAC0B,UAAU,CAAC,CAAC;EACnB;;EAEA;EACA,MAAcA,UAAUA,CAAA,EAAG;IACzB,IAAI;MACF;MACA,MAAMC,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB;MAC1C,IAAIH,KAAK,EAAE;QACT,IAAI,CAACpC,GAAG,GAAGoC,KAAK;MAClB,CAAC,MAAM;QACL;QACA,MAAMI,OAAO,GAAGH,OAAO,CAACC,GAAG,CAACG,iBAAiB,IAAI,8BAA8B;QAC/E,IAAI,CAACzC,GAAG,GAAGwC,OAAO,CAACE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MACjE;MAEAhC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACX,GAAG,CAAC;;MAEvC;MACA,IAAI,CAACC,KAAK,GAAG0C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE1C;MACA,IAAI,IAAI,CAAC3C,KAAK,EAAE;QACd,IAAI,CAAC4C,OAAO,CAAC,CAAC;MAChB,CAAC,MAAM;QACLnC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF;;EAEA;EACAoB,WAAWA,CAACxC,QAAgC,EAAE;IAC5C,IAAI,CAACA,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAGA;IAAS,CAAC;EACnD;;EAEA;EACAyC,WAAWA,CAAC9C,KAAoB,EAAE;IAChC,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,IAAIA,KAAK,EAAE;MACT;MACA,IAAI,CAAC4C,OAAO,CAAC,CAAC;IAChB,CAAC,MAAM;MACL;MACA,IAAI,CAACG,UAAU,CAAC,CAAC;IACnB;EACF;;EAEA;EACAH,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAACxC,YAAY,IAAK,IAAI,CAACN,MAAM,IAAI,IAAI,CAACA,MAAM,CAACkD,UAAU,KAAKC,SAAS,CAACC,IAAK,EAAE;MACnF;IACF;IAEA,IAAI,CAAC9C,YAAY,GAAG,IAAI;IAExB,IAAI;MACF;MACA,IAAI,CAAC,IAAI,CAACJ,KAAK,EAAE;QACf,IAAI,CAACA,KAAK,GAAG0C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC3C,KAAK,EAAE;UACfS,OAAO,CAAC0C,IAAI,CAAC,4DAA4D,CAAC;UAC1E,IAAI,CAAC/C,YAAY,GAAG,KAAK;UACzB;QACF;MACF;;MAEA;MACA;MACA,MAAMgD,SAAS,GAAG,IAAI,CAACrD,GAAG,CAACsD,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAACtD,GAAG,GAAG,GAAG,IAAI,CAACA,GAAG,KAAK;MACxE,MAAMoC,KAAK,GAAG,GAAGiB,SAAS,UAAU,IAAI,CAACpD,KAAK,EAAE;MAChDS,OAAO,CAACC,GAAG,CAAC,4BAA4ByB,KAAK,EAAE,CAAC;;MAEhD;MACA,IAAI,CAACrC,MAAM,GAAG,IAAImD,SAAS,CAACd,KAAK,CAAC;;MAElC;MACA,IAAI,CAACrC,MAAM,CAACwD,MAAM,GAAG,IAAI,CAAC/C,UAAU,CAACgD,IAAI,CAAC,IAAI,CAAC;MAC/C,IAAI,CAACzD,MAAM,CAAC0D,SAAS,GAAG,IAAI,CAACxC,aAAa,CAACuC,IAAI,CAAC,IAAI,CAAC;MACrD,IAAI,CAACzD,MAAM,CAAC2D,OAAO,GAAG,IAAI,CAAC/B,WAAW,CAAC6B,IAAI,CAAC,IAAI,CAAC;MACjD,IAAI,CAACzD,MAAM,CAAC4D,OAAO,GAAG,IAAI,CAAC1B,WAAW,CAACuB,IAAI,CAAC,IAAI,CAAC;IACnD,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAI,CAACrB,YAAY,GAAG,KAAK;MACzB,IAAI,CAAC2B,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;EACAgB,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACjD,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC6D,KAAK,CAAC,CAAC;MACnB,IAAI,CAAC7D,MAAM,GAAG,IAAI;IACpB;IAEA,IAAI,IAAI,CAACQ,iBAAiB,EAAE;MAC1BuB,aAAa,CAAC,IAAI,CAACvB,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAC/B;IAEA,IAAI,CAACF,YAAY,GAAG,KAAK;IACzB,IAAI,CAACH,iBAAiB,GAAG,CAAC;EAC5B;EAyGA;EACAW,WAAWA,CAACK,OAA4C,EAAE;IACxD,IAAI,IAAI,CAACnB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACkD,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;MAC5D,MAAMU,WAA6B,GAAG;QACpC,GAAG3C,OAAO;QACV4C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC;MACD,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAAC9C,IAAI,CAAC+C,SAAS,CAACL,WAAW,CAAC,CAAC;IAC/C,CAAC,MAAM;MACLnD,OAAO,CAAC0C,IAAI,CAAC,iDAAiD,CAAC;IACjE;EACF;;EAEA;EACQxC,cAAcA,CAAA,EAAG;IACvB,IAAI,CAACL,iBAAiB,GAAG4D,WAAW,CAAC,MAAM;MACzC,IAAI,CAACtD,WAAW,CAAC;QACfC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACb;;EAEA;EACQiB,iBAAiBA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAAC9B,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;MACvDO,OAAO,CAACgB,KAAK,CAAC,8CAA8C,CAAC;MAC7D;IACF;IAEA,IAAI,CAACxB,iBAAiB,EAAE;IACxB,MAAMkE,KAAK,GAAG,IAAI,CAAChE,iBAAiB,GAAGiE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACpE,iBAAiB,GAAG,CAAC,CAAC;IAE9EQ,OAAO,CAACC,GAAG,CAAC,6CAA6C,IAAI,CAACT,iBAAiB,OAAOkE,KAAK,IAAI,CAAC;IAEhGG,UAAU,CAAC,MAAM;MACf,IAAI,CAAC,IAAI,CAACxE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACkD,UAAU,KAAKC,SAAS,CAACsB,MAAM,EAAE;QAC/D,IAAI,CAAC3B,OAAO,CAAC,CAAC;MAChB;IACF,CAAC,EAAEuB,KAAK,CAAC;EACX;;EAEA;EACAK,WAAWA,CAAA,EAAY;IACrB,OAAO,IAAI,CAAC1E,MAAM,KAAK,IAAI,IAAI,IAAI,CAACA,MAAM,CAACkD,UAAU,KAAKC,SAAS,CAACC,IAAI;EAC1E;;EAEA;EACAuB,kBAAkBA,CAAA,EAAW;IAC3B,IAAI,CAAC,IAAI,CAAC3E,MAAM,EAAE,OAAO,cAAc;IAEvC,QAAQ,IAAI,CAACA,MAAM,CAACkD,UAAU;MAC5B,KAAKC,SAAS,CAACyB,UAAU;QAAE,OAAO,YAAY;MAC9C,KAAKzB,SAAS,CAACC,IAAI;QAAE,OAAO,WAAW;MACvC,KAAKD,SAAS,CAAC0B,OAAO;QAAE,OAAO,SAAS;MACxC,KAAK1B,SAAS,CAACsB,MAAM;QAAE,OAAO,QAAQ;MACtC;QAAS,OAAO,SAAS;IAC3B;EACF;AACF;;AAEA;AACA,OAAO,MAAMK,gBAAgB,GAAG,IAAIhF,gBAAgB,CAAC,CAAC;AACtD,eAAegF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}