[{"D:\\新项目1\\finalproject\\frontend\\src\\index.tsx": "1", "D:\\新项目1\\finalproject\\frontend\\src\\reportWebVitals.ts": "2", "D:\\新项目1\\finalproject\\frontend\\src\\App.tsx": "3", "D:\\新项目1\\finalproject\\frontend\\src\\components\\Header.tsx": "4", "D:\\新项目1\\finalproject\\frontend\\src\\components\\Tetris.tsx": "5", "D:\\新项目1\\finalproject\\frontend\\src\\components\\HomePage.tsx": "6", "D:\\新项目1\\finalproject\\frontend\\src\\components\\SlotMachine.tsx": "7", "D:\\新项目1\\finalproject\\frontend\\src\\contexts\\AppContext.tsx": "8", "D:\\新项目1\\finalproject\\frontend\\src\\components\\BottomNavigation.tsx": "9", "D:\\新项目1\\finalproject\\frontend\\src\\components\\Minesweeper.tsx": "10", "D:\\新项目1\\finalproject\\frontend\\src\\components\\GameHall.tsx": "11", "D:\\新项目1\\finalproject\\frontend\\src\\components\\Snake.tsx": "12", "D:\\新项目1\\finalproject\\frontend\\src\\components\\WalletPage.tsx": "13", "D:\\新项目1\\finalproject\\frontend\\src\\components\\ProfilePage.tsx": "14", "D:\\新项目1\\finalproject\\frontend\\src\\components\\LoadingSpinner.tsx": "15", "D:\\新项目1\\finalproject\\frontend\\src\\components\\LeaderboardPage.tsx": "16", "D:\\新项目1\\finalproject\\frontend\\src\\components\\NotificationCenter.tsx": "17", "D:\\新项目1\\finalproject\\frontend\\src\\components\\WalletInstallGuide.tsx": "18", "D:\\新项目1\\finalproject\\frontend\\src\\components\\RecentWinnings.tsx": "19", "D:\\新项目1\\finalproject\\frontend\\src\\components\\ExchangeModal.tsx": "20", "D:\\新项目1\\finalproject\\frontend\\src\\components\\WinModal.tsx": "21", "D:\\新项目1\\finalproject\\frontend\\src\\services\\api.ts": "22", "D:\\新项目1\\finalproject\\frontend\\src\\services\\websocket.ts": "23", "D:\\新项目1\\finalproject\\frontend\\src\\services\\wallet.ts": "24"}, {"size": 673, "mtime": 1754180176313, "results": "25", "hashOfConfig": "26"}, {"size": 425, "mtime": 1754172579432, "results": "27", "hashOfConfig": "26"}, {"size": 3638, "mtime": 1754199810927, "results": "28", "hashOfConfig": "26"}, {"size": 5506, "mtime": 1754225206903, "results": "29", "hashOfConfig": "26"}, {"size": 5448, "mtime": 1754172579437, "results": "30", "hashOfConfig": "26"}, {"size": 8000, "mtime": 1754175019718, "results": "31", "hashOfConfig": "26"}, {"size": 17636, "mtime": 1754202590162, "results": "32", "hashOfConfig": "26"}, {"size": 12289, "mtime": 1754184017730, "results": "33", "hashOfConfig": "26"}, {"size": 1580, "mtime": 1754174574894, "results": "34", "hashOfConfig": "26"}, {"size": 6242, "mtime": 1754172579437, "results": "35", "hashOfConfig": "26"}, {"size": 7214, "mtime": 1754202560445, "results": "36", "hashOfConfig": "26"}, {"size": 5171, "mtime": 1754172579437, "results": "37", "hashOfConfig": "26"}, {"size": 25610, "mtime": 1754201897679, "results": "38", "hashOfConfig": "26"}, {"size": 9462, "mtime": 1754174618277, "results": "39", "hashOfConfig": "26"}, {"size": 954, "mtime": 1754172579433, "results": "40", "hashOfConfig": "26"}, {"size": 10392, "mtime": 1754224832103, "results": "41", "hashOfConfig": "26"}, {"size": 6655, "mtime": 1754172579433, "results": "42", "hashOfConfig": "26"}, {"size": 4780, "mtime": 1754172579434, "results": "43", "hashOfConfig": "26"}, {"size": 5238, "mtime": 1754202439234, "results": "44", "hashOfConfig": "26"}, {"size": 9994, "mtime": 1754190725042, "results": "45", "hashOfConfig": "26"}, {"size": 5391, "mtime": 1754172579436, "results": "46", "hashOfConfig": "26"}, {"size": 10727, "mtime": 1754270100169, "results": "47", "hashOfConfig": "26"}, {"size": 8428, "mtime": 1754270916228, "results": "48", "hashOfConfig": "26"}, {"size": 19821, "mtime": 1754191084786, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "21s4l1", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\新项目1\\finalproject\\frontend\\src\\index.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\App.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\Header.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\Tetris.tsx", ["122"], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\HomePage.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\SlotMachine.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\contexts\\AppContext.tsx", ["123"], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\BottomNavigation.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\Minesweeper.tsx", ["124"], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\GameHall.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\Snake.tsx", ["125"], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\WalletPage.tsx", ["126", "127"], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\ProfilePage.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\LeaderboardPage.tsx", ["128", "129", "130"], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\NotificationCenter.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\WalletInstallGuide.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\RecentWinnings.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\ExchangeModal.tsx", ["131"], [], "D:\\新项目1\\finalproject\\frontend\\src\\components\\WinModal.tsx", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\services\\api.ts", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\services\\websocket.ts", [], [], "D:\\新项目1\\finalproject\\frontend\\src\\services\\wallet.ts", [], [], {"ruleId": "132", "severity": 1, "message": "133", "line": 60, "column": 6, "nodeType": "134", "endLine": 60, "endColumn": 65, "suggestions": "135"}, {"ruleId": "132", "severity": 1, "message": "136", "line": 139, "column": 6, "nodeType": "134", "endLine": 139, "endColumn": 8, "suggestions": "137"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 47, "column": 6, "nodeType": "134", "endLine": 47, "endColumn": 38, "suggestions": "138"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 37, "column": 6, "nodeType": "134", "endLine": 37, "endColumn": 29, "suggestions": "139"}, {"ruleId": "140", "severity": 1, "message": "141", "line": 17, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 17, "endColumn": 27}, {"ruleId": "132", "severity": 1, "message": "144", "line": 31, "column": 6, "nodeType": "134", "endLine": 31, "endColumn": 8, "suggestions": "145"}, {"ruleId": "140", "severity": 1, "message": "146", "line": 25, "column": 6, "nodeType": "142", "messageId": "143", "endLine": 25, "endColumn": 12}, {"ruleId": "132", "severity": 1, "message": "147", "line": 37, "column": 6, "nodeType": "134", "endLine": 37, "endColumn": 8, "suggestions": "148"}, {"ruleId": "140", "severity": 1, "message": "149", "line": 80, "column": 9, "nodeType": "142", "messageId": "143", "endLine": 80, "endColumn": 29}, {"ruleId": "132", "severity": 1, "message": "150", "line": 26, "column": 6, "nodeType": "134", "endLine": 26, "endColumn": 43, "suggestions": "151"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleGameEnd'. Either include it or remove the dependency array.", "ArrayExpression", ["152"], "React Hook useEffect has a missing dependency: 'initializeApp'. Either include it or remove the dependency array.", ["153"], ["154"], ["155"], "@typescript-eslint/no-unused-vars", "'showManualDeposit' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'loadWalletBalance'. Either include it or remove the dependency array.", ["156"], "'Player' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLeaderboards'. Either include it or remove the dependency array.", ["157"], "'getUsernameMaxLength' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'calculatePreview'. Either include it or remove the dependency array.", ["158"], {"desc": "159", "fix": "160"}, {"desc": "161", "fix": "162"}, {"desc": "163", "fix": "164"}, {"desc": "165", "fix": "166"}, {"desc": "167", "fix": "168"}, {"desc": "169", "fix": "170"}, {"desc": "171", "fix": "172"}, "Update the dependencies array to be: [gameStarted, gameOver, score, lines, level, gameStartTime, handleGameEnd]", {"range": "173", "text": "174"}, "Update the dependencies array to be: [initializeApp]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [gameStarted, gameOver, gameWon, handleGameEnd]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [gameStarted, gameOver, handleGameEnd]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [loadWalletBalance]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [loadLeaderboards]", {"range": "183", "text": "184"}, "Update the dependencies array to be: [amount, exchangeType, exchangeRates, calculatePreview]", {"range": "185", "text": "186"}, [1707, 1766], "[gameStarted, gameOver, score, lines, level, gameStartTime, handleGameEnd]", [3481, 3483], "[initializeApp]", [1386, 1418], "[gameStarted, gameOver, gameWon, handleGameEnd]", [1030, 1053], "[gameStarted, gameOver, handleGameEnd]", [1351, 1353], "[loadWalletBalance]", [930, 932], "[loadLeaderboards]", [833, 870], "[amount, exchangeType, exchangeRates, calculatePreview]"]