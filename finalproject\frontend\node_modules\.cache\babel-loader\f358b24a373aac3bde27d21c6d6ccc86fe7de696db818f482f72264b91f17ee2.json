{"ast": null, "code": "import axios from 'axios';\n\n// API配置\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8081/api/v1';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use(config => {\n  // 对于配置相关的公开API，不需要认证token\n  const publicConfigPaths = ['/config/system', '/config/token-contract', '/config/project-wallet'];\n  const isPublicConfigPath = publicConfigPaths.some(path => {\n    var _config$url;\n    return (_config$url = config.url) === null || _config$url === void 0 ? void 0 : _config$url.includes(path);\n  });\n  if (!isPublicConfigPath) {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理通用错误\napi.interceptors.response.use(response => {\n  var _response$config$url;\n  // 检查是否是兑换API的直接返回格式\n  if ((_response$config$url = response.config.url) !== null && _response$config$url !== void 0 && _response$config$url.includes('/transaction/exchange') && response.data.success !== undefined) {\n    // 兑换API直接返回 {success: true, tokens_received: 10000, ...}\n    return response;\n  }\n\n  // 其他API的标准格式: { code: 200, message: \"success\", data: {...} }\n  if (response.data.code === 200) {\n    return response.data.data;\n  } else {\n    throw new Error(response.data.message || 'API Error');\n  }\n}, error => {\n  var _error$response, _error$response2, _error$response2$data;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token过期或无效，清除本地存储并跳转到登录\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.reload();\n  }\n  return Promise.reject(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message);\n});\n\n// 类型定义\n\n// API服务类\nclass ApiService {\n  // 认证相关\n  async login(walletAddress, signature, message) {\n    return api.post('/auth/login', {\n      wallet_address: walletAddress,\n      signature: signature,\n      message: message\n    });\n  }\n\n  // 用户相关\n  async getUserProfile() {\n    return api.get('/user/profile');\n  }\n  async updateUserProfile(data) {\n    return api.put('/user/profile', data);\n  }\n  async getUserBalance() {\n    return api.get('/user/balance');\n  }\n  async getTransactionHistory(page = 1, pageSize = 20) {\n    return api.get(`/user/transactions?page=${page}&page_size=${pageSize}`);\n  }\n\n  // 游戏相关\n  async playSlotMachine(betAmount, lines = 25) {\n    return api.post('/game/slot-machine/bet', {\n      bet_amount: betAmount,\n      lines: lines\n    });\n  }\n  async getSlotMachineHistory(page = 1, pageSize = 10) {\n    return api.get(`/game/slot-machine/history?page=${page}&page_size=${pageSize}`);\n  }\n  async getSlotMachineStats() {\n    return api.get('/game/slot-machine/stats');\n  }\n  async getGameConfigs() {\n    return api.get('/game/configs');\n  }\n\n  // 其他游戏相关\n  async playTetris(score, lines, level, gameTime) {\n    return api.post('/game/tetris/play', {\n      score,\n      lines,\n      level,\n      game_time: gameTime\n    });\n  }\n  async playSnake(score, length, gameTime) {\n    return api.post('/game/snake/play', {\n      score,\n      length,\n      game_time: gameTime\n    });\n  }\n  async playMinesweeper(score, gameTime, isCompleted, minesFound) {\n    return api.post('/game/minesweeper/play', {\n      score,\n      game_time: gameTime,\n      is_completed: isCompleted,\n      mines_found: minesFound\n    });\n  }\n\n  // 排行榜相关\n  async getDepositLeaderboard() {\n    return api.get('/user/leaderboard/deposit');\n  }\n  async getPointsLeaderboard() {\n    return api.get('/user/leaderboard/points');\n  }\n  async getWinsLeaderboard() {\n    return api.get('/user/leaderboard/wins');\n  }\n\n  // 配置相关\n  async getSystemConfigs() {\n    return api.get('/config/system');\n  }\n  async getTokenConfigs() {\n    return api.get('/config/token');\n  }\n  async getExchangeRates() {\n    return api.get('/config/exchange-rates');\n  }\n\n  // 交易相关 - 真实API对接\n  async deposit(amount, txHash) {\n    return api.post('/transaction/deposit', {\n      amount: amount,\n      tx_hash: txHash\n    });\n  }\n\n  // 处理充值（带重试机制）\n  async processDeposit(amount, txHash) {\n    return api.post('/transaction/deposit', {\n      amount: amount,\n      tx_hash: txHash\n    });\n  }\n  async withdraw(amount, toAddress) {\n    return api.post('/transaction/withdraw', {\n      amount: amount,\n      to_address: toAddress\n    });\n  }\n  async exchangePointsToTokens(pointsAmount) {\n    const response = await api.post('/transaction/exchange', {\n      from_type: 'points',\n      to_type: 'token',\n      amount: pointsAmount\n    });\n    // 后端现在直接返回期望的格式\n    return response.data;\n  }\n  async exchangeTokensToPoints(tokenAmount) {\n    const response = await api.post('/transaction/exchange', {\n      from_type: 'token',\n      to_type: 'points',\n      amount: tokenAmount\n    });\n    // 后端现在直接返回期望的格式\n    return response.data;\n  }\n\n  // 获取钱包链上余额\n  async getWalletBalance(walletAddress) {\n    const params = walletAddress ? `?wallet_address=${walletAddress}` : '';\n    return api.get(`/user/wallet-balance${params}`);\n  }\n\n  // 通知系统\n  async getNotifications(page = 1, pageSize = 20) {\n    return api.get(`/user/notifications?page=${page}&page_size=${pageSize}`);\n  }\n  async markNotificationAsRead(notificationId) {\n    return api.put(`/user/notifications/${notificationId}/read`);\n  }\n\n  // 游戏扩展功能\n  async getSlotMachineSymbols() {\n    return api.get('/game/slot-machine/symbols');\n  }\n  async getSlotMachinePaylines() {\n    return api.get('/game/slot-machine/paylines');\n  }\n\n  // 全部标记通知已读\n  async markAllNotificationsAsRead() {\n    return api.put('/user/notifications/read-all');\n  }\n  async getRecentWinnings(limit = 10) {\n    const result = await api.get(`/recent-winnings?limit=${limit}`);\n    // 确保返回数组，即使API返回null\n    return Array.isArray(result) ? result : [];\n  }\n\n  // 高级配置\n  async getTokenConfigForFrontend() {\n    return api.get('/config/token');\n  }\n\n  // 获取项目方钱包地址\n  async getProjectWallet() {\n    return api.get('/config/project-wallet');\n  }\n\n  // 获取代币合约地址\n  async getTokenContract() {\n    return api.get('/config/token-contract');\n  }\n\n  // 实时数据刷新\n  async refreshUserData() {\n    return api.get('/user/refresh');\n  }\n}\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "publicConfigPaths", "isPublicConfigPath", "some", "path", "_config$url", "url", "includes", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_response$config$url", "data", "success", "undefined", "code", "Error", "message", "_error$response", "_error$response2", "_error$response2$data", "status", "removeItem", "window", "location", "reload", "ApiService", "login", "wallet<PERSON>ddress", "signature", "post", "wallet_address", "getUserProfile", "get", "updateUserProfile", "put", "getUserBalance", "getTransactionHistory", "page", "pageSize", "playSlotMachine", "betAmount", "lines", "bet_amount", "getSlotMachineHistory", "getSlotMachineStats", "getGameConfigs", "playTetris", "score", "level", "gameTime", "game_time", "playSnake", "length", "playMinesweeper", "isCompleted", "minesFound", "is_completed", "mines_found", "getDepositLeaderboard", "getPointsLeaderboard", "getWinsLeaderboard", "getSystemConfigs", "getTokenConfigs", "getExchangeRates", "deposit", "amount", "txHash", "tx_hash", "processDeposit", "withdraw", "to<PERSON><PERSON><PERSON>", "to_address", "exchangePointsToTokens", "pointsAmount", "from_type", "to_type", "exchangeTokensToPoints", "tokenAmount", "getWalletBalance", "params", "getNotifications", "markNotificationAsRead", "notificationId", "getSlotMachineSymbols", "getSlotMachinePaylines", "markAllNotificationsAsRead", "getRecentWinnings", "limit", "result", "Array", "isArray", "getTokenConfigForFrontend", "getProjectWallet", "getTokenContract", "refreshUserData", "apiService"], "sources": ["D:/新项目1/finalproject/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from 'axios';\n\n// API配置\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8081/api/v1';\n\n// 创建axios实例\nconst api: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use(\n  (config) => {\n    // 对于配置相关的公开API，不需要认证token\n    const publicConfigPaths = ['/config/system', '/config/token-contract', '/config/project-wallet'];\n    const isPublicConfigPath = publicConfigPaths.some(path => config.url?.includes(path));\n\n    if (!isPublicConfigPath) {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理通用错误\napi.interceptors.response.use(\n  (response: AxiosResponse) => {\n    // 检查是否是兑换API的直接返回格式\n    if (response.config.url?.includes('/transaction/exchange') && response.data.success !== undefined) {\n      // 兑换API直接返回 {success: true, tokens_received: 10000, ...}\n      return response;\n    }\n\n    // 其他API的标准格式: { code: 200, message: \"success\", data: {...} }\n    if (response.data.code === 200) {\n      return response.data.data;\n    } else {\n      throw new Error(response.data.message || 'API Error');\n    }\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token过期或无效，清除本地存储并跳转到登录\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.reload();\n    }\n    return Promise.reject(error.response?.data?.message || error.message);\n  }\n);\n\n// 类型定义\nexport interface User {\n  id: string;\n  wallet_address: string;\n  nickname: string;\n  token_balance: string | number;\n  points_balance: string | number;\n  language: string;\n  is_whitelist: boolean;\n  status: string;\n  last_login_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface GameResult {\n  id: string;\n  user_id: string;\n  game_type: string;\n  bet_amount: number;\n  win_amount: number;\n  result_data: any;\n  created_at: string;\n}\n\nexport interface GamePlayResult {\n  game_id: string;\n  game_type: string;\n  score: number;\n  reward: number;\n  new_balance: number;\n  is_high_score: boolean;\n  message: string;\n}\n\nexport interface Transaction {\n  id: string;\n  user_id: string;\n  type: string;\n  amount: number;\n  status: string;\n  created_at: string;\n  description?: string;\n  extra_data?: string;\n}\n\nexport interface SlotMachineResult {\n  symbols: string[][];\n  win_amount: number;\n  win_lines: any[];\n  multiplier: number;\n  is_jackpot: boolean;\n  balance_after: number;\n  total_win: number;\n  bet_amount: number;\n}\n\nexport interface LeaderboardEntry {\n  rank: number;\n  user_id: string;\n  nickname: string;\n  amount: number;\n  avatar?: string;\n}\n\nexport interface Notification {\n  id: string;\n  user_id: string;\n  type: string;\n  title: string;\n  content: string;\n  data?: any;\n  is_read: boolean;\n  created_at: string;\n}\n\nexport interface RecentWinning {\n  id: string;\n  user_nickname: string;\n  user_id: string;\n  game_type: string;\n  win_amount: number;\n  created_at: string;\n}\n\n// API服务类\nclass ApiService {\n  // 认证相关\n  async login(walletAddress: string, signature: string, message: string): Promise<{ token: string; user: User }> {\n    return api.post('/auth/login', {\n      wallet_address: walletAddress,\n      signature: signature,\n      message: message,\n    });\n  }\n\n  // 用户相关\n  async getUserProfile(): Promise<User> {\n    return api.get('/user/profile');\n  }\n\n  async updateUserProfile(data: Partial<User>): Promise<User> {\n    return api.put('/user/profile', data);\n  }\n\n  async getUserBalance(): Promise<{ balance: number; points_balance: number; token_balance?: number }> {\n    return api.get('/user/balance');\n  }\n\n  async getTransactionHistory(page: number = 1, pageSize: number = 20): Promise<{\n    items: Transaction[];\n    total: number;\n    page: number;\n    page_size: number;\n  }> {\n    return api.get(`/user/transactions?page=${page}&page_size=${pageSize}`);\n  }\n\n  // 游戏相关\n  async playSlotMachine(betAmount: number, lines: number = 25): Promise<SlotMachineResult> {\n    return api.post('/game/slot-machine/bet', {\n      bet_amount: betAmount,\n      lines: lines,\n    });\n  }\n\n  async getSlotMachineHistory(page: number = 1, pageSize: number = 10): Promise<{\n    records: GameResult[];\n    total: number;\n    page: number;\n    page_size: number;\n  }> {\n    return api.get(`/game/slot-machine/history?page=${page}&page_size=${pageSize}`);\n  }\n\n  async getSlotMachineStats(): Promise<{\n    total_games: number;\n    total_bet: number;\n    total_win: number;\n    biggest_win: number;\n    win_rate: number;\n  }> {\n    return api.get('/game/slot-machine/stats');\n  }\n\n  async getGameConfigs(): Promise<{\n    slot_machine: {\n      min_bet: number;\n      max_bet: number;\n      symbols: string[];\n      paylines: number;\n    };\n  }> {\n    return api.get('/game/configs');\n  }\n\n  // 其他游戏相关\n  async playTetris(score: number, lines: number, level: number, gameTime: number): Promise<GamePlayResult> {\n    return api.post('/game/tetris/play', {\n      score,\n      lines,\n      level,\n      game_time: gameTime,\n    });\n  }\n\n  async playSnake(score: number, length: number, gameTime: number): Promise<GamePlayResult> {\n    return api.post('/game/snake/play', {\n      score,\n      length,\n      game_time: gameTime,\n    });\n  }\n\n  async playMinesweeper(score: number, gameTime: number, isCompleted: boolean, minesFound: number): Promise<GamePlayResult> {\n    return api.post('/game/minesweeper/play', {\n      score,\n      game_time: gameTime,\n      is_completed: isCompleted,\n      mines_found: minesFound,\n    });\n  }\n\n  // 排行榜相关\n  async getDepositLeaderboard(): Promise<LeaderboardEntry[]> {\n    return api.get('/user/leaderboard/deposit');\n  }\n\n  async getPointsLeaderboard(): Promise<LeaderboardEntry[]> {\n    return api.get('/user/leaderboard/points');\n  }\n\n  async getWinsLeaderboard(): Promise<LeaderboardEntry[]> {\n    return api.get('/user/leaderboard/wins');\n  }\n\n  // 配置相关\n  async getSystemConfigs(): Promise<any> {\n    return api.get('/config/system');\n  }\n\n  async getTokenConfigs(): Promise<any> {\n    return api.get('/config/token');\n  }\n\n  async getExchangeRates(): Promise<any> {\n    return api.get('/config/exchange-rates');\n  }\n\n  // 交易相关 - 真实API对接\n  async deposit(amount: number, txHash: string): Promise<{\n    success: boolean;\n    transaction_id: string;\n    message?: string;\n  }> {\n    return api.post('/transaction/deposit', {\n      amount: amount,\n      tx_hash: txHash,\n    });\n  }\n\n  // 处理充值（带重试机制）\n  async processDeposit(amount: number, txHash: string): Promise<any> {\n    return api.post('/transaction/deposit', {\n      amount: amount,\n      tx_hash: txHash,\n    });\n  }\n\n  async withdraw(amount: number, toAddress: string): Promise<{\n    success: boolean;\n    transaction_id: string;\n    message?: string;\n  }> {\n    return api.post('/transaction/withdraw', {\n      amount: amount,\n      to_address: toAddress,\n    });\n  }\n\n  async exchangePointsToTokens(pointsAmount: number): Promise<{\n    success: boolean;\n    tokens_received: number;\n    exchange_rate: number;\n    transaction_id: string;\n  }> {\n    const response = await api.post('/transaction/exchange', {\n      from_type: 'points',\n      to_type: 'token',\n      amount: pointsAmount,\n    });\n    // 后端现在直接返回期望的格式\n    return response.data;\n  }\n\n  async exchangeTokensToPoints(tokenAmount: number): Promise<{\n    success: boolean;\n    points_received: number;\n    exchange_rate: number;\n    transaction_id: string;\n  }> {\n    const response = await api.post('/transaction/exchange', {\n      from_type: 'token',\n      to_type: 'points',\n      amount: tokenAmount,\n    });\n    // 后端现在直接返回期望的格式\n    return response.data;\n  }\n\n  // 获取钱包链上余额\n  async getWalletBalance(walletAddress?: string): Promise<{ balance: number; last_updated: string }> {\n    const params = walletAddress ? `?wallet_address=${walletAddress}` : '';\n    return api.get(`/user/wallet-balance${params}`);\n  }\n\n  // 通知系统\n  async getNotifications(page: number = 1, pageSize: number = 20): Promise<{\n    notifications: Notification[];\n    total: number;\n    unread_count: number;\n    page: number;\n    page_size: number;\n  }> {\n    return api.get(`/user/notifications?page=${page}&page_size=${pageSize}`);\n  }\n\n  async markNotificationAsRead(notificationId: string): Promise<{ success: boolean }> {\n    return api.put(`/user/notifications/${notificationId}/read`);\n  }\n\n  // 游戏扩展功能\n  async getSlotMachineSymbols(): Promise<{\n    symbols: Array<{\n      symbol: string;\n      name: string;\n      multiplier: number;\n      rarity: string;\n    }>;\n  }> {\n    return api.get('/game/slot-machine/symbols');\n  }\n\n  async getSlotMachinePaylines(): Promise<{\n    paylines: Array<{\n      line_id: number;\n      positions: number[][];\n      description: string;\n    }>;\n  }> {\n    return api.get('/game/slot-machine/paylines');\n  }\n\n  // 全部标记通知已读\n  async markAllNotificationsAsRead(): Promise<{ success: boolean; updated_count: number }> {\n    return api.put('/user/notifications/read-all');\n  }\n\n  async getRecentWinnings(limit: number = 10): Promise<RecentWinning[]> {\n    const result = await api.get(`/recent-winnings?limit=${limit}`);\n    // 确保返回数组，即使API返回null\n    return Array.isArray(result) ? result : [];\n  }\n\n  // 高级配置\n  async getTokenConfigForFrontend(): Promise<{\n    token_name: string;\n    token_symbol: string;\n    token_decimals: number;\n    contract_address: string;\n    project_wallet: string;\n    exchange_rates: {\n      points_to_tokens: number;\n      tokens_to_points: number;\n    };\n  }> {\n    return api.get('/config/token');\n  }\n\n  // 获取项目方钱包地址\n  async getProjectWallet(): Promise<{ project_wallet: string }> {\n    return api.get('/config/project-wallet');\n  }\n\n  // 获取代币合约地址\n  async getTokenContract(): Promise<{ token_contract: string }> {\n    return api.get('/config/token-contract');\n  }\n\n  // 实时数据刷新\n  async refreshUserData(): Promise<{\n    user: User;\n    balance: { token_balance: number; points_balance: number };\n    notifications_count: number;\n  }> {\n    return api.get('/user/refresh');\n  }\n}\n\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;;AAE3D;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;;AAEpF;AACA,MAAMC,GAAkB,GAAGL,KAAK,CAACM,MAAM,CAAC;EACtCC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,iBAAiB,GAAG,CAAC,gBAAgB,EAAE,wBAAwB,EAAE,wBAAwB,CAAC;EAChG,MAAMC,kBAAkB,GAAGD,iBAAiB,CAACE,IAAI,CAACC,IAAI;IAAA,IAAAC,WAAA;IAAA,QAAAA,WAAA,GAAIL,MAAM,CAACM,GAAG,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,QAAQ,CAACH,IAAI,CAAC;EAAA,EAAC;EAErF,IAAI,CAACF,kBAAkB,EAAE;IACvB,MAAMM,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTR,MAAM,CAACJ,OAAO,CAACe,aAAa,GAAG,UAAUH,KAAK,EAAE;IAClD;EACF;EACA,OAAOR,MAAM;AACf,CAAC,EACAY,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACApB,GAAG,CAACK,YAAY,CAACkB,QAAQ,CAAChB,GAAG,CAC1BgB,QAAuB,IAAK;EAAA,IAAAC,oBAAA;EAC3B;EACA,IAAI,CAAAA,oBAAA,GAAAD,QAAQ,CAACf,MAAM,CAACM,GAAG,cAAAU,oBAAA,eAAnBA,oBAAA,CAAqBT,QAAQ,CAAC,uBAAuB,CAAC,IAAIQ,QAAQ,CAACE,IAAI,CAACC,OAAO,KAAKC,SAAS,EAAE;IACjG;IACA,OAAOJ,QAAQ;EACjB;;EAEA;EACA,IAAIA,QAAQ,CAACE,IAAI,CAACG,IAAI,KAAK,GAAG,EAAE;IAC9B,OAAOL,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B,CAAC,MAAM;IACL,MAAM,IAAII,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACK,OAAO,IAAI,WAAW,CAAC;EACvD;AACF,CAAC,EACAV,KAAK,IAAK;EAAA,IAAAW,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACT,IAAI,EAAAF,eAAA,GAAAX,KAAK,CAACG,QAAQ,cAAAQ,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;IAClC;IACAjB,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC;IAChClB,YAAY,CAACkB,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;EACA,OAAOjB,OAAO,CAACC,MAAM,CAAC,EAAAU,gBAAA,GAAAZ,KAAK,CAACG,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBH,OAAO,KAAIV,KAAK,CAACU,OAAO,CAAC;AACvE,CACF,CAAC;;AAED;;AAqFA;AACA,MAAMS,UAAU,CAAC;EACf;EACA,MAAMC,KAAKA,CAACC,aAAqB,EAAEC,SAAiB,EAAEZ,OAAe,EAA0C;IAC7G,OAAO9B,GAAG,CAAC2C,IAAI,CAAC,aAAa,EAAE;MAC7BC,cAAc,EAAEH,aAAa;MAC7BC,SAAS,EAAEA,SAAS;MACpBZ,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMe,cAAcA,CAAA,EAAkB;IACpC,OAAO7C,GAAG,CAAC8C,GAAG,CAAC,eAAe,CAAC;EACjC;EAEA,MAAMC,iBAAiBA,CAACtB,IAAmB,EAAiB;IAC1D,OAAOzB,GAAG,CAACgD,GAAG,CAAC,eAAe,EAAEvB,IAAI,CAAC;EACvC;EAEA,MAAMwB,cAAcA,CAAA,EAAiF;IACnG,OAAOjD,GAAG,CAAC8C,GAAG,CAAC,eAAe,CAAC;EACjC;EAEA,MAAMI,qBAAqBA,CAACC,IAAY,GAAG,CAAC,EAAEC,QAAgB,GAAG,EAAE,EAKhE;IACD,OAAOpD,GAAG,CAAC8C,GAAG,CAAC,2BAA2BK,IAAI,cAAcC,QAAQ,EAAE,CAAC;EACzE;;EAEA;EACA,MAAMC,eAAeA,CAACC,SAAiB,EAAEC,KAAa,GAAG,EAAE,EAA8B;IACvF,OAAOvD,GAAG,CAAC2C,IAAI,CAAC,wBAAwB,EAAE;MACxCa,UAAU,EAAEF,SAAS;MACrBC,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EAEA,MAAME,qBAAqBA,CAACN,IAAY,GAAG,CAAC,EAAEC,QAAgB,GAAG,EAAE,EAKhE;IACD,OAAOpD,GAAG,CAAC8C,GAAG,CAAC,mCAAmCK,IAAI,cAAcC,QAAQ,EAAE,CAAC;EACjF;EAEA,MAAMM,mBAAmBA,CAAA,EAMtB;IACD,OAAO1D,GAAG,CAAC8C,GAAG,CAAC,0BAA0B,CAAC;EAC5C;EAEA,MAAMa,cAAcA,CAAA,EAOjB;IACD,OAAO3D,GAAG,CAAC8C,GAAG,CAAC,eAAe,CAAC;EACjC;;EAEA;EACA,MAAMc,UAAUA,CAACC,KAAa,EAAEN,KAAa,EAAEO,KAAa,EAAEC,QAAgB,EAA2B;IACvG,OAAO/D,GAAG,CAAC2C,IAAI,CAAC,mBAAmB,EAAE;MACnCkB,KAAK;MACLN,KAAK;MACLO,KAAK;MACLE,SAAS,EAAED;IACb,CAAC,CAAC;EACJ;EAEA,MAAME,SAASA,CAACJ,KAAa,EAAEK,MAAc,EAAEH,QAAgB,EAA2B;IACxF,OAAO/D,GAAG,CAAC2C,IAAI,CAAC,kBAAkB,EAAE;MAClCkB,KAAK;MACLK,MAAM;MACNF,SAAS,EAAED;IACb,CAAC,CAAC;EACJ;EAEA,MAAMI,eAAeA,CAACN,KAAa,EAAEE,QAAgB,EAAEK,WAAoB,EAAEC,UAAkB,EAA2B;IACxH,OAAOrE,GAAG,CAAC2C,IAAI,CAAC,wBAAwB,EAAE;MACxCkB,KAAK;MACLG,SAAS,EAAED,QAAQ;MACnBO,YAAY,EAAEF,WAAW;MACzBG,WAAW,EAAEF;IACf,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMG,qBAAqBA,CAAA,EAAgC;IACzD,OAAOxE,GAAG,CAAC8C,GAAG,CAAC,2BAA2B,CAAC;EAC7C;EAEA,MAAM2B,oBAAoBA,CAAA,EAAgC;IACxD,OAAOzE,GAAG,CAAC8C,GAAG,CAAC,0BAA0B,CAAC;EAC5C;EAEA,MAAM4B,kBAAkBA,CAAA,EAAgC;IACtD,OAAO1E,GAAG,CAAC8C,GAAG,CAAC,wBAAwB,CAAC;EAC1C;;EAEA;EACA,MAAM6B,gBAAgBA,CAAA,EAAiB;IACrC,OAAO3E,GAAG,CAAC8C,GAAG,CAAC,gBAAgB,CAAC;EAClC;EAEA,MAAM8B,eAAeA,CAAA,EAAiB;IACpC,OAAO5E,GAAG,CAAC8C,GAAG,CAAC,eAAe,CAAC;EACjC;EAEA,MAAM+B,gBAAgBA,CAAA,EAAiB;IACrC,OAAO7E,GAAG,CAAC8C,GAAG,CAAC,wBAAwB,CAAC;EAC1C;;EAEA;EACA,MAAMgC,OAAOA,CAACC,MAAc,EAAEC,MAAc,EAIzC;IACD,OAAOhF,GAAG,CAAC2C,IAAI,CAAC,sBAAsB,EAAE;MACtCoC,MAAM,EAAEA,MAAM;MACdE,OAAO,EAAED;IACX,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,cAAcA,CAACH,MAAc,EAAEC,MAAc,EAAgB;IACjE,OAAOhF,GAAG,CAAC2C,IAAI,CAAC,sBAAsB,EAAE;MACtCoC,MAAM,EAAEA,MAAM;MACdE,OAAO,EAAED;IACX,CAAC,CAAC;EACJ;EAEA,MAAMG,QAAQA,CAACJ,MAAc,EAAEK,SAAiB,EAI7C;IACD,OAAOpF,GAAG,CAAC2C,IAAI,CAAC,uBAAuB,EAAE;MACvCoC,MAAM,EAAEA,MAAM;MACdM,UAAU,EAAED;IACd,CAAC,CAAC;EACJ;EAEA,MAAME,sBAAsBA,CAACC,YAAoB,EAK9C;IACD,MAAMhE,QAAQ,GAAG,MAAMvB,GAAG,CAAC2C,IAAI,CAAC,uBAAuB,EAAE;MACvD6C,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,OAAO;MAChBV,MAAM,EAAEQ;IACV,CAAC,CAAC;IACF;IACA,OAAOhE,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMiE,sBAAsBA,CAACC,WAAmB,EAK7C;IACD,MAAMpE,QAAQ,GAAG,MAAMvB,GAAG,CAAC2C,IAAI,CAAC,uBAAuB,EAAE;MACvD6C,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,QAAQ;MACjBV,MAAM,EAAEY;IACV,CAAC,CAAC;IACF;IACA,OAAOpE,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMmE,gBAAgBA,CAACnD,aAAsB,EAAsD;IACjG,MAAMoD,MAAM,GAAGpD,aAAa,GAAG,mBAAmBA,aAAa,EAAE,GAAG,EAAE;IACtE,OAAOzC,GAAG,CAAC8C,GAAG,CAAC,uBAAuB+C,MAAM,EAAE,CAAC;EACjD;;EAEA;EACA,MAAMC,gBAAgBA,CAAC3C,IAAY,GAAG,CAAC,EAAEC,QAAgB,GAAG,EAAE,EAM3D;IACD,OAAOpD,GAAG,CAAC8C,GAAG,CAAC,4BAA4BK,IAAI,cAAcC,QAAQ,EAAE,CAAC;EAC1E;EAEA,MAAM2C,sBAAsBA,CAACC,cAAsB,EAAiC;IAClF,OAAOhG,GAAG,CAACgD,GAAG,CAAC,uBAAuBgD,cAAc,OAAO,CAAC;EAC9D;;EAEA;EACA,MAAMC,qBAAqBA,CAAA,EAOxB;IACD,OAAOjG,GAAG,CAAC8C,GAAG,CAAC,4BAA4B,CAAC;EAC9C;EAEA,MAAMoD,sBAAsBA,CAAA,EAMzB;IACD,OAAOlG,GAAG,CAAC8C,GAAG,CAAC,6BAA6B,CAAC;EAC/C;;EAEA;EACA,MAAMqD,0BAA0BA,CAAA,EAAyD;IACvF,OAAOnG,GAAG,CAACgD,GAAG,CAAC,8BAA8B,CAAC;EAChD;EAEA,MAAMoD,iBAAiBA,CAACC,KAAa,GAAG,EAAE,EAA4B;IACpE,MAAMC,MAAM,GAAG,MAAMtG,GAAG,CAAC8C,GAAG,CAAC,0BAA0BuD,KAAK,EAAE,CAAC;IAC/D;IACA,OAAOE,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;EAC5C;;EAEA;EACA,MAAMG,yBAAyBA,CAAA,EAU5B;IACD,OAAOzG,GAAG,CAAC8C,GAAG,CAAC,eAAe,CAAC;EACjC;;EAEA;EACA,MAAM4D,gBAAgBA,CAAA,EAAwC;IAC5D,OAAO1G,GAAG,CAAC8C,GAAG,CAAC,wBAAwB,CAAC;EAC1C;;EAEA;EACA,MAAM6D,gBAAgBA,CAAA,EAAwC;IAC5D,OAAO3G,GAAG,CAAC8C,GAAG,CAAC,wBAAwB,CAAC;EAC1C;;EAEA;EACA,MAAM8D,eAAeA,CAAA,EAIlB;IACD,OAAO5G,GAAG,CAAC8C,GAAG,CAAC,eAAe,CAAC;EACjC;AACF;AAEA,OAAO,MAAM+D,UAAU,GAAG,IAAItE,UAAU,CAAC,CAAC;AAC1C,eAAesE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}